import { parentPort, workerData } from 'worker_threads';
import { createStructuredPrompt } from '../../utils/promptTemplates';
import { MODEL_PROVIDERS } from '../../../constants';

interface WorkerData {
  prompt: string;
  searchResults: Array<{
    text: string;
    score: number;
    source: string;
  }>;
  modelId?: string;
  chatHistory?: string;
  provider?: string;
  apiKey?: string;
}

interface ModelResponse {
  response?: string;
  content?: string;
  done?: boolean;
}

const {
  prompt,
  searchResults,
  modelId = 'llama3.2:1b',
  chatHistory = '',
  provider = 'Ollama',
  apiKey = 'DUMMY_API_KEY'
} = workerData as WorkerData;

async function generateResponse(): Promise<void> {
  try {
    const structuredPrompt = createStructuredPrompt(searchResults, prompt, chatHistory);

    // Get provider configuration
    const providerConfig = MODEL_PROVIDERS.find(p => p.MODEL_PROVIDER === provider);
    if (!providerConfig) {
      throw new Error(`Provider ${provider} not found`);
    }

    let responseText: string;

    if (provider === 'Ollama') {
      // Use direct Ollama API for backward compatibility
      responseText = await generateWithOllama(structuredPrompt, modelId);
    } else {
      // Use OpenAI-compatible API for other providers
      responseText = await generateWithOpenAI(structuredPrompt, modelId, providerConfig.BASE_URL, apiKey);
    }

    parentPort?.postMessage({
      type: 'response',
      content: responseText
    });
  } catch (error) {
    parentPort?.postMessage({
      type: 'error',
      message: error instanceof Error ? error.message : String(error),
    });
  }
}

async function generateWithOllama(prompt: string, model: string): Promise<string> {
  const response = await fetch('http://localhost:11434/api/generate', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      model: model,
      prompt: prompt,
      stream: false,
      options: {
        temperature: 0.3,
        num_predict: 512,
      }
    })
  });

  if (!response.ok) {
    throw new Error(`Ollama generation failed: ${response.statusText}`);
  }

  const result = await response.json() as ModelResponse;

  if (!result.response) {
    throw new Error('No text was generated by Ollama');
  }

  return result.response;
}

async function generateWithOpenAI(prompt: string, model: string, baseURL: string, apiKey: string): Promise<string> {
  const response = await fetch(`${baseURL}/chat/completions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model: model,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 512,
    })
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`OpenAI-compatible API generation failed: ${response.status} ${response.statusText} - ${errorText}`);
  }

  const result = await response.json();

  if (!result.choices || !result.choices[0] || !result.choices[0].message || !result.choices[0].message.content) {
    throw new Error('No text was generated by the model');
  }

  return result.choices[0].message.content;
}

// Start processing
generateResponse();
