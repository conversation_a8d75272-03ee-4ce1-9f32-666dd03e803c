import fs from 'fs';
import path from 'path';
import { MODEL } from '../../../constants';

interface WorkerData {
  query: string;
  embeddingDirPath: string;
}

interface SearchResult {
  text: string;
  score: number;
  source: string;
}

interface Metadata {
  id: number;
  filename: string;
  text: string;
  embedding: number[];
  url: string;
}

// Parse command line arguments
const args = process.argv.slice(2);
if (args.length === 0) {
  console.error('No arguments provided');
  process.exit(1);
}

const workerDataStr = args[0];
let workerData: WorkerData;

try {
  workerData = JSON.parse(workerDataStr);
} catch (error) {
  console.error('Failed to parse worker data:', error);
  process.exit(1);
}

const { query, embeddingDirPath } = workerData;

// Calculate cosine similarity between two vectors
function cosineSimilarity(a: number[], b: number[]): number {
  const dotProduct = a.reduce((sum, val, i) => sum + val * b[i], 0);
  const normA = Math.sqrt(a.reduce((sum, val) => sum + val * val, 0));
  const normB = Math.sqrt(b.reduce((sum, val) => sum + val * val, 0));
  return dotProduct / (normA * normB);
}

async function searchEmbeddings(): Promise<void> {
  try {
    // Check if embeddings exist
    const indexPath = path.join(embeddingDirPath, 'index.json');
    if (!fs.existsSync(indexPath)) {
      throw new Error(`Embedding index not found at: ${indexPath}`);
    }

    // Load index data
    const indexData = JSON.parse(fs.readFileSync(indexPath, 'utf8'));

    // Create embedding for query
    const queryEmbedding = await createEmbeddingForText(query);

    // Load all embeddings and calculate similarities
    const similarities: Array<{ id: number; score: number }> = [];

    for (let i = 0; i < indexData.total; i++) {
      const metadataPath = path.join(embeddingDirPath, `${i}.json`);

      if (!fs.existsSync(metadataPath)) {
        continue;
      }

      try {
        const metadata: Metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
        const similarity = cosineSimilarity(queryEmbedding, metadata.embedding);
        similarities.push({ id: i, score: similarity });
      } catch (fileError) {
        continue;
      }
    }

    // Sort by similarity score and get top k results
    const k = 3;
    const topResults = similarities
      .sort((a, b) => b.score - a.score)
      .slice(0, k);

    // Load metadata and prepare results
    const results = await Promise.all(
      topResults.map(async ({ id, score }) => {
        const metadataPath = path.join(embeddingDirPath, `${id}.json`);
        const metadata: Metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
        return {
          text: metadata.text,
          score: score,
          source: metadata.url
        } as SearchResult;
      })
    );

    // Send results back to parent process
    console.log(JSON.stringify(results));
    process.exit(0);

  } catch (error) {
    console.error(JSON.stringify({
      type: 'error',
      message: error instanceof Error ? error.message : String(error)
    }));
    process.exit(1);
  }
}

// Create embedding using Ollama API
async function createEmbeddingForText(text: string): Promise<number[]> {
  try {
    console.log('Creating embedding for text length:', text.length);
    console.log('Using model:', MODEL.DEFAULT_TEXT_EMBEDDING_MODEL);
    console.log('Ollama API endpoint: http://localhost:11434/api/embeddings');

    const requestBody = {
      model: MODEL.DEFAULT_TEXT_EMBEDDING_MODEL,
      prompt: text
    };

    console.log('Request body:', JSON.stringify(requestBody, null, 2));

    const response = await fetch('http://localhost:11434/api/embeddings', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody)
    });

    console.log('Response status:', response.status);
    console.log('Response status text:', response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Response error text:', errorText);
      throw new Error(`Failed to generate embeddings: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    console.log('Response result keys:', Object.keys(result));

    if (!result.embedding || !Array.isArray(result.embedding)) {
      console.error('Invalid embedding response:', result);
      throw new Error('Invalid embedding response from Ollama');
    }

    console.log('Embedding created successfully, length:', result.embedding.length);
    return result.embedding;
  } catch (error) {
    console.error('Error creating embedding:', error);
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }
    throw error;
  }
}

// Start processing
searchEmbeddings();